'use client'

import { useMemo, useRef, useState } from 'react'
import { Carousel } from 'antd'
import type { CarouselRef } from 'antd/es/carousel'

import { CarouselArrow, Skeleton } from '@/components'
import { IconSoundPlay } from '@/components/icons'

import { useProduct } from '../../context/ProductContext'

import { MagnifierImage } from './MagnifierImage'
import ThumbnailCarousel from './ThumbnailCarousel'
import VideoPlayer from './VideoPlayer'

import './style.css'

interface GalleryItem {
  id: number
  type: 'image' | 'video'
  url: string
  video_url?: string
  label: string
  image_url?: string
  alt?: string
}

interface ProductMedia {
  type?: 'image' | 'video'
  url: string
  label?: string
}

interface ProductDetails {
  video_url?: string
  video_image?: string
  media_gallery?: ProductMedia[]
  digital_has_audio?: boolean
}

// 定义产品状态类型
interface ProductStatusType {
  images?: GalleryItem[]
  popVisible?: boolean
  parentSku?: string
  sku?: string
  quantity?: number
  servicePrice?: number
}

const ProductGallery = ({
  setSoundPopVisible,
}: {
  setSoundPopVisible: (visible: boolean) => void
}) => {
  const [activeIndex, setActiveIndex] = useState(0)
  const [magnifierData, setMagnifierData] = useState({
    showMagnifier: false,
    imgX: 0,
    imgY: 0,
  })

  const carouselRef = useRef<CarouselRef>(null)
  const thumbCarouselRef = useRef<CarouselRef>(null)
  const videoRefs = useRef<(HTMLVideoElement | null)[]>([])
  const videoPauseFns = useRef<(() => void)[]>([])

  const {
    productDetails: product,
    productStatus,
    carouselRender,
  } = useProduct() as {
    productDetails: ProductDetails
    productStatus: ProductStatusType
    carouselRender: boolean
  }

  // 处理视频数据
  const videoItems = useMemo(() => {
    const arr: GalleryItem[] = []
    if (product?.video_url) {
      arr.push({
        id: 0,
        type: 'video',
        url: product.video_image || '',
        video_url: product.video_url,
        label: '产品视频',
      })
    }
    return arr
  }, [product])

  // 处理图片数据
  const galleryItems = useMemo<GalleryItem[]>(() => {
    const banners = productStatus?.images || []
    return [...videoItems, ...banners].map((item, i) => ({
      ...item,
      id: i + 1,
      type: item.type || 'image',
      url: item.url,
      image_url: item.url,
      label: item.label || `产品图片 ${i + 1}`,
      alt: item.label,
    }))
  }, [productStatus, videoItems])

  // 停止所有视频播放
  const stopAllVideos = () => {
    // 使用新的暂停方法
    videoPauseFns.current.forEach((pauseFn) => {
      if (pauseFn) {
        pauseFn()
      }
    })

    // 备用方案：直接暂停原生视频元素
    videoRefs.current.forEach((videoRef) => {
      if (videoRef && !videoRef.paused) {
        videoRef.pause()
      }
    })
  }

  // 处理轮播切换前
  const handleBeforeChange = (_: number, next: number) => {
    // 停止所有视频播放
    stopAllVideos()

    setActiveIndex(next)

    // 直接跳转到对应的缩略图位置
    thumbCarouselRef.current?.goTo(next)
  }

  // 处理缩略图点击
  const handleThumbnailClick = (index: number) => {
    // 停止所有视频播放
    stopAllVideos()

    setActiveIndex(index)
    carouselRef.current?.goTo(index)
  }

  // 注册视频引用
  const registerVideoRef = (index: number, videoRef: HTMLVideoElement | null) => {
    videoRefs.current[index] = videoRef
  }

  // 注册视频暂停方法
  const registerVideoPauseFn = (index: number, pauseFn: (() => void) | null) => {
    if (pauseFn) {
      videoPauseFns.current[index] = pauseFn
    } else {
      delete videoPauseFns.current[index]
    }
  }

  return (
    <div className="relative flex w-full flex-col">
      {carouselRender && galleryItems.length > 0 ? (
        <div>
          {/* 主图轮播 */}
          <div className="relative">
            <Carousel
              style={{
                width: '100%',
                height: 'auto',
                background: '#FFFFFF',
              }}
              infinite={false}
              arrows
              prevArrow={
                galleryItems.length > 1 ? (
                  <CarouselArrow
                    disabled={activeIndex === 0}
                    styleName="responsive-carousel-arrow-left"
                    isShow={true}
                    theme="pdp"
                  />
                ) : undefined
              }
              nextArrow={
                galleryItems.length > 1 ? (
                  <CarouselArrow
                    disabled={activeIndex === galleryItems.length - 1}
                    styleName="responsive-carousel-arrow-right"
                    isNext
                    isShow={true}
                    theme="pdp"
                  />
                ) : undefined
              }
              dots={false}
              ref={carouselRef}
              beforeChange={handleBeforeChange}>
              {galleryItems.map((image, index) =>
                image.type === 'video' ? (
                  <VideoPlayer
                    key={index}
                    videoUrl={image.video_url}
                    posterUrl={image.url}
                    onVideoRef={(ref) => registerVideoRef(index, ref)}
                    onPauseRef={(pauseFn) => registerVideoPauseFn(index, pauseFn)}
                  />
                ) : (
                  <MagnifierImage
                    key={index}
                    src={image.url}
                    alt={image.label || `Product image ${index + 1}`}
                    onMagnifierChange={setMagnifierData}
                  />
                ),
              )}
            </Carousel>

            {/* 轮播页码显示 */}
            {galleryItems.length > 1 && galleryItems[activeIndex]?.type !== 'video' && (
              <div className="absolute bottom-base-16 left-1/2 flex -translate-x-1/2 justify-center font-miSansRegular330 text-[18px] text-[#0F0F0F]">
                <span>{activeIndex + 1}</span>/
                <span className="text-[#86868B]">{galleryItems.length}</span>
              </div>
            )}
          </div>

          {product?.digital_has_audio && (
            <div
              onClick={() => setSoundPopVisible(true)}
              className="absolute right-[27px] top-[30px] flex h-[56px] w-[105px] cursor-pointer justify-center rounded-full bg-white px-[9px] py-[6px]">
              <div className="flex flex-row items-center gap-2">
                <IconSoundPlay />
                <div className="text-base text-black">试听</div>
              </div>
            </div>
          )}

          {/* 放大区域 - 仅对图片显示放大效果 */}
          {magnifierData.showMagnifier && galleryItems[activeIndex].type === 'image' && (
            <div
              className="responsive-magnifier-area responsive-magnifier-bg absolute z-10 overflow-hidden bg-white"
              style={{
                left: '100%',
                top: '36px',
                backgroundImage: `url(${galleryItems[activeIndex].url})`,
                backgroundPosition: `${magnifierData.imgX}% ${magnifierData.imgY}%`,
                backgroundRepeat: 'no-repeat',
              }}
            />
          )}

          {/* 缩略图轮播 */}
          <div className="mt-base-16">
            <ThumbnailCarousel
              ref={thumbCarouselRef}
              images={galleryItems}
              activeIndex={activeIndex}
              onThumbnailClick={handleThumbnailClick}
            />
          </div>
        </div>
      ) : (
        <div>
          {/* 主图骨架屏 */}
          <div className="responsive-product-gallery mx-auto rounded-[20px] bg-[#F8F8F9]"></div>

          {/* 缩略图骨架屏 */}
          <div className="mt-base-16">
            <div className="responsive-thumbnail-container mx-auto">
              <div className="flex justify-center">
                {Array.from({ length: 6 }, (_, index) => (
                  <div key={index} className="responsive-thumbnail-spacing">
                    <div className="responsive-thumbnail-item">
                      <Skeleton width="100%" height="100%" borderRadius={8} />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ProductGallery
